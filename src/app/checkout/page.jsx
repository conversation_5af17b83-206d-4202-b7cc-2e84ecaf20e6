"use client";

import { useEffect, useState } from "react";
import { useAppSelector } from "@/lib/hooks/redux";
import { Button } from "@/components/ui/button";
import { FaArrowRight } from "react-icons/fa6";
import { useRouter } from "next/navigation";
import { createOrder } from "@/lib/services/order";
import { useRazorpay } from "react-razorpay";
import { toast } from "react-hot-toast";
import { useDispatch } from "react-redux";
import { clearCart } from "@/lib/features/carts/cartsSlice";
import { fetchAddresses } from "@/lib/features/addresses/addressSlice";
import AddAddressModal from "../my-profile/AddAddressModal";
import EditAddressModal from "../my-profile/EditAddressModal";

export default function CheckoutPage() {
  const { cart, totalPrice, adjustedTotalPrice } = useAppSelector(
    (state) => state.carts
  );
  const { addresses } = useAppSelector((state) => state.addresses);
  const router = useRouter();
  const { Razorpay } = useRazorpay();
  const dispatch = useDispatch();
  const token = localStorage.getItem("token");

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zip: "",
  });

  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zip: "",
  });

  // if (!cart || cart.items.length === 0) {
  //   router.push("/");
  //   return null;
  // }

  // Calculate shipping cost based on state
  const baseShippingCost = 75.0;
  let shippingCost = baseShippingCost;
  const [isAddressOpen, setIsAddressOpen] = useState(false);
  const [isEditAddressOpen, setIsEditAddressOpen] = useState(false);
  const [addressToEdit, setAddressToEdit] = useState(null);
  const params = new URLSearchParams(window.location.search);
  const isTest = params.get("is_test") === "true";

  // Calculate tax (you can adjust this based on your business logic)
  const taxRate = 0.18; // 18% GST
  let taxAmount = 0;

  if (isTest) {
    shippingCost = 0;
    taxAmount = 0;
  }

  if (adjustedTotalPrice > 999) {
    shippingCost = 0;
  }

  const totalSavings = totalPrice - adjustedTotalPrice;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  useEffect(() => {
    dispatch(fetchAddresses());
  }, []);

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...errors };

    if (!sameAsShipping) {
      if (!formData.firstName.trim()) {
        // Validate firstName
        newErrors.firstName = "First name is required";
        valid = false;
      }

      // Validate lastName
      if (!formData.lastName.trim()) {
        newErrors.lastName = "Last name is required";
        valid = false;
      }

      // Validate email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!formData.email.trim() || !emailRegex.test(formData.email)) {
        newErrors.email = "Valid email is required";
        valid = false;
      }

      // Validate phone
      const phoneRegex = /^[0-9]{10}$/;
      if (!formData.phone.trim() || !phoneRegex.test(formData.phone)) {
        newErrors.phone = "Valid 10-digit phone number is required";
        valid = false;
      }

      // Validate address
      if (!formData.address.trim()) {
        newErrors.address = "Address is required";
        valid = false;
      }

      // Validate city
      if (!formData.city.trim()) {
        newErrors.city = "City is required";
        valid = false;
      }

      // Validate state
      if (!formData.state.trim()) {
        newErrors.state = "State is required";
        valid = false;
      }

      // Validate zip
      const zipRegex = /^[0-9]{6}$/;
      if (!formData.zip.trim() || !zipRegex.test(formData.zip)) {
        newErrors.zip = "Valid 6-digit PIN code is required";
        valid = false;
      }
    }

    setErrors(newErrors);
    return valid;
  };

  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      toast.dismiss();
      toast.error("Please fix the errors in the form");
      return;
    }

    if (!selectedAddress) {
      toast.dismiss();
      toast.error("Please select address for processing order.");
      return;
    }
    try {
      // Prepare the payload according to the new API structure
      const orderPayload = {
        products: cart?.items.map((item) => ({
          id: item.id,
          qty: item.quantity,
          price: item.price,
        })),
        shipping_amount: shippingCost,
        tax_amount: taxAmount,
        total_amount: parseFloat(totalAmount).toFixed(2),
        payment_method: "razorpay",
        shipping_method: "default",
        notes: "Order from checkout",
        address: addresses.find((address) => address.id === selectedAddress),
        billing_address_same_as_shipping: sameAsShipping,
        billing_address: {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          country: "India",
          zip_code: formData.zip,
        },
      };

      const response = await createOrder(orderPayload);

      if (!response.success) {
        toast.error(response.message || "Failed to create order");
        return;
      }

      const { order_id, order_code, razorpay_order_id, amount, currency } =
        response.data;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZOR_PAY_ID,
        amount: amount, // Amount is already in paise from the API
        currency: currency,
        name: "CromiTopia",
        description: "Checkout Payment",
        order_id: razorpay_order_id,
        handler: async function (res) {
          try {
            // After successful payment, you might want to update the order status
            // or redirect to a success page
            const updatePayload = {
              order_id: order_id,
              razorpay_payment_id: res.razorpay_payment_id,
              razorpay_order_id: res.razorpay_order_id,
              razorpay_signature: res.razorpay_signature,
              payment_status: "paid",
              order_status: "confirmed",
            };

            // Call your API to update payment details (you'll need to create this endpoint)
            const completeRes = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL}/process-order`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(updatePayload),
              }
            );

            const completeData = await completeRes.json();
            if (completeData.success) {
              toast.dismiss();
              toast.success("Order placed successfully!");
              dispatch(clearCart());
              setTimeout(() => {
                router.push("/orders");
              }, 10);
            } else {
              toast.dismiss();
              toast.error(
                "Payment successful but failed to update order status."
              );
              // Still redirect to orders page as payment was successful
              dispatch(clearCart());
              setTimeout(() => {
                router.push("/orders");
              }, 1000);
            }
          } catch (error) {
            toast.dismiss();
            toast.error(
              "Payment successful but something went wrong while updating order."
            );
            // Still redirect as payment was successful
            dispatch(clearCart());
            setTimeout(() => {
              router.push("/orders");
            }, 1000);
          }
        },
        prefill: {
          name: `${formData.firstName} ${formData.lastName}`,
          email: formData.email,
          contact: formData.phone,
        },
        notes: {
          address: formData.address,
          order_code: order_code,
        },
        theme: {
          color: "#000000",
        },
        modal: {
          ondismiss: () => {
            toast.dismiss();
            toast.error("Payment cancelled by the user.");
          },
        },
      };

      const rzp = new Razorpay(options);
      rzp.on("payment.failed", function (response) {
        toast.dismiss();
        toast.error("Payment failed. Please try again.");
      });
      rzp.open();
    } catch (error) {
      toast.dismiss();
      toast.error("Something went wrong. Please try again later.");
    }
  };
  const [selectedAddress, setSelectedAddress] = useState(addresses[0]?.id);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [sameAsShipping, setSameAsShipping] = useState(true);

  // Check if selected address city is "surat" and set delivery charge to zero
  const selectedAddressData = addresses.find(
    (address) => address.id === selectedAddress
  );
  if (
    selectedAddressData &&
    selectedAddressData.city &&
    selectedAddressData.city.toLowerCase() === "surat"
  ) {
    shippingCost = 0;
  }

  // Calculate total amount after all shipping cost adjustments
  let totalAmount = adjustedTotalPrice + shippingCost + taxAmount;
  return (
    <>
      <div className="min-h-screen bg-gray-100 flex justify-center py-10">
        <div className="w-full max-w-6xl bg-white shadow-lg rounded-lg flex flex-col md:flex-row p-6">
          {/* Left Section - Delivery Info */}
          <div className="w-full md:w-2/3 md:p-6">
            <h2 className="text-2xl font-semibold mb-4">Delivery</h2>

            {/* Address Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">
                Select Delivery Address
              </h3>
              <div className="space-y-4">
                {addresses.map((address) => (
                  <label
                    key={address.id}
                    className="flex items-start border rounded-md p-4 cursor-pointer"
                  >
                    <input
                      type="radio"
                      name="selectedAddress"
                      value={address.id}
                      className="mr-3 mt-1"
                      onChange={() => setSelectedAddress(address.id)}
                      checked={selectedAddress === address.id}
                    />
                    <div>
                      <p className="font-semibold">{address.name}</p>
                      <p className="text-sm">{address.full_address}</p>
                      <p className="text-sm">{address.phone}</p>
                    </div>
                  </label>
                ))}
              </div>
              <button
                onClick={() => setIsAddressOpen(!isAddressOpen)}
                className="text-blue-600 mt-4 text-sm"
              >
                Add New Address
              </button>
            </div>

            <div className="mt-8">
              <h3 className="text-lg font-medium mb-4">Billing Address</h3>
              <label className="flex items-center mb-4">
                <input
                  type="checkbox"
                  className="mr-3"
                  onChange={() => setSameAsShipping(!sameAsShipping)}
                  checked={sameAsShipping}
                />
                Same as Shipping Address
              </label>

              {!sameAsShipping && (
                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">
                    Enter Billing Address
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <input
                        name="firstName"
                        placeholder="First name"
                        className={`border p-3 rounded-md w-full ${
                          errors.firstName ? "border-red-500" : ""
                        }`}
                        onChange={handleChange}
                        value={formData.firstName}
                      />
                      {errors.firstName && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.firstName}
                        </p>
                      )}
                    </div>
                    <div>
                      <input
                        name="lastName"
                        placeholder="Last name"
                        className={`border p-3 rounded-md w-full ${
                          errors.lastName ? "border-red-500" : ""
                        }`}
                        onChange={handleChange}
                        value={formData.lastName}
                      />
                      {errors.lastName && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.lastName}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="mt-4">
                    <input
                      name="email"
                      type="email"
                      placeholder="Email"
                      className={`border p-3 rounded-md w-full ${
                        errors.email ? "border-red-500" : ""
                      }`}
                      onChange={handleChange}
                      value={formData.email}
                    />
                    {errors.email && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.email}
                      </p>
                    )}
                  </div>

                  <div className="mt-4">
                    <input
                      name="phone"
                      type="tel"
                      placeholder="Phone"
                      className={`border p-3 rounded-md w-full ${
                        errors.phone ? "border-red-500" : ""
                      }`}
                      onChange={handleChange}
                      value={formData.phone}
                    />
                    {errors.phone && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.phone}
                      </p>
                    )}
                  </div>

                  <div className="mt-4">
                    <input
                      name="address"
                      placeholder="Address"
                      className={`border p-3 rounded-md w-full ${
                        errors.address ? "border-red-500" : ""
                      }`}
                      onChange={handleChange}
                      value={formData.address}
                    />
                    {errors.address && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.address}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-4 mt-4">
                    <div>
                      <input
                        name="city"
                        placeholder="City"
                        className={`border p-3 rounded-md w-full ${
                          errors.city ? "border-red-500" : ""
                        }`}
                        onChange={handleChange}
                        value={formData.city}
                      />
                      {errors.city && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.city}
                        </p>
                      )}
                    </div>
                    <div>
                      <input
                        name="state"
                        placeholder="State"
                        className={`border p-3 rounded-md w-full ${
                          errors.state ? "border-red-500" : ""
                        }`}
                        onChange={handleChange}
                        value={formData.state}
                      />
                      {errors.state && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.state}
                        </p>
                      )}
                    </div>
                    <div>
                      <input
                        name="zip"
                        placeholder="PIN code"
                        className={`border p-3 rounded-md w-full ${
                          errors.zip ? "border-red-500" : ""
                        }`}
                        onChange={handleChange}
                        value={formData.zip}
                      />
                      {errors.zip && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.zip}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="w-full md:w-1/3 bg-gray-50 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

            {cart?.items?.map((product) => (
              <div key={product.id} className="flex items-center border-b py-3">
                <img
                  src={product.image_url}
                  alt={product.name}
                  width={50}
                  height={50}
                  className="rounded-md mr-4"
                />
                <div className="flex-1">
                  <h3 className="text-sm font-medium">{product.name}</h3>
                  <p className="text-gray-500 text-xs">
                    Qty: {product.quantity}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-gray-500 line-through text-xs">
                    ₹{product.original_price}
                  </p>
                  <p className="font-semibold">₹{product.price}</p>
                </div>
              </div>
            ))}

            <div className="flex justify-between mt-4">
              <span className="font-semibold">Subtotal</span>
              <span>₹{adjustedTotalPrice.toFixed(2)}</span>
            </div>
            {/* <div className="flex justify-between mt-2">
              <span className="font-semibold">Tax (18% GST)</span>
              <span>₹{taxAmount.toFixed(2)}</span>
            </div> */}
            <div className="flex justify-between mt-2">
              <span className="font-semibold">Shipping</span>
              <span>₹{shippingCost.toFixed(2)}</span>
            </div>

            <hr className="my-4" />

            <div className="flex justify-between text-lg font-bold">
              <span>Total</span>
              <span>₹{totalAmount.toFixed(2)}</span>
            </div>

            {/* <p className="text-sm text-green-600 mt-2">🤑 TOTAL SAVINGS ₹{totalSavings.toFixed(2)}</p> */}

            <Button
              type="button"
              onClick={handlePlaceOrder}
              className="text-sm md:text-base font-medium bg-primary rounded-full w-full py-4 h-[54px] md:h-[60px] group mt-6"
            >
              Place Order
              <FaArrowRight className="text-xl ml-2 group-hover:translate-x-1 transition-all" />
            </Button>
          </div>
        </div>
      </div>
      <AddAddressModal
        isOpen={isAddressOpen}
        onClose={() => setIsAddressOpen(false)}
      />
      <EditAddressModal
        isOpen={isEditAddressOpen}
        onClose={() => setIsEditAddressOpen(false)}
        address={addressToEdit}
      />
    </>
  );
}
