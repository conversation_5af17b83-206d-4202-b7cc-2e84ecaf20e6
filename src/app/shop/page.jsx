"use client";

import BreadcrumbShop from "@/components/shop-page/BreadcrumbShop";
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MobileFilters from "@/components/shop-page/filters/MobileFilters";
import Filters from "@/components/shop-page/filters";
import ProductCard from "@/components/common/ProductCard";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useSearch } from "@/lib/context/SearchContext";
import SpinnerbLoader from "@/components/ui/SpinnerbLoader";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

export default function ShopPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { searchTerm } = useSearch();

  // Get URL parameters
  const isArrival = searchParams?.get("is_new_arrival") === "true";
  const initialFilter = searchParams?.get("filter") ? 6 : isArrival ? 5 : "";
  const urlPage = parseInt(searchParams?.get("page")) || 1;
  const urlSort = searchParams?.get("sort") || "low-price";
  const urlCategory = searchParams?.get("categories");

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(urlPage);
  const [totalRecord, setTotalRecord] = useState(1);
  const [sort, setSort] = useState(urlSort);
  const [selectedCategory, setSelectedCategory] = useState(urlCategory || null);
  const [categoryInitialized, setCategoryInitialized] = useState(false);
  const [activeFilter, setActiveFilter] = useState(initialFilter);
  const [invalidCategory, setInvalidCategory] = useState(false);

  const itemsPerPage = 12;

  // Function to update URL parameters
  const updateURL = (newParams) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    Object.entries(newParams).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        current.delete(key);
      } else if (key === "page" && value === 1) {
        // Remove page=1 from URL for cleaner URLs
        current.delete(key);
      } else {
        current.set(key, value);
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : "";
    router.push(`${pathname}${query}`, { scroll: false });
  };

  // Function to generate clean URLs for pagination
  const generatePageURL = (pageNum) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    if (pageNum === 1) {
      current.delete("page");
    } else {
      current.set("page", pageNum.toString());
    }
    const search = current.toString();
    return search ? `${pathname}?${search}` : pathname;
  };
  const firstProductIndex =
    totalRecord > 0 && currentPage >= 1
      ? (currentPage - 1) * itemsPerPage + 1
      : 0;
  const lastProductIndex = Math.min(
    currentPage * itemsPerPage,
    totalRecord * itemsPerPage
  );

  // Sync URL parameters with state
  useEffect(() => {
    const urlPage = parseInt(searchParams?.get("page")) || 1;
    const urlSort = searchParams?.get("sort") || "low-price";
    const urlCategory = searchParams?.get("categories");

    setCurrentPage(urlPage);
    setSort(urlSort);
    setSelectedCategory(urlCategory || null);

    // Reset invalid category flag when URL changes
    setInvalidCategory(false);
  }, [searchParams]);

  useEffect(() => {
    // Clean up any old localStorage category data
    const storedCategory = localStorage.getItem("selectedCategory");
    if (storedCategory) {
      localStorage.removeItem("selectedCategory");
    }
    setCategoryInitialized(true); // ✅ Mark it as ready
  }, []);

  useEffect(() => {
    if (!categoryInitialized) return;
    const fetchProducts = async () => {
      try {
        setLoading(true);

        let url = `${apiRoutes.PRODUCTS}?page=${currentPage}&per_page=${itemsPerPage}&sort-by=${sort === "low-price" ? "price_asc" : "price_desc"}`;
        if (selectedCategory) {
          // Send category slug directly to backend
          url += `&categories=${selectedCategory}`;
        }

        if (searchTerm) {
          url += `&q=${searchTerm}`;
        }

        if (activeFilter) {
          url += `&collection=${activeFilter}`;
        }

        const response = await apiServiceWrapper.get(url);

        if (!response.error) {
          // const decryptedText = decryptData(response.data);
          // const responseData = decryptedText && JSON.parse(decryptedText);
          const responseData = response.data;

          if (responseData) {
            setProducts(responseData);
            setTotalRecord(response.meta.total || 0);
            setInvalidCategory(false); // Valid response, reset invalid flag
          } else {
            setProducts([]);
            setTotalRecord(0);
          }
        } else {
          // Check if error is due to invalid category
          if (selectedCategory && response.message?.includes("category")) {
            setInvalidCategory(true);
            setProducts([]);
            setTotalRecord(0);
          } else {
            throw new Error(response.message || "Failed to fetch products");
          }
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [
    currentPage,
    sort,
    selectedCategory,
    categoryInitialized,
    activeFilter,
    searchTerm,
  ]);

  return (
    <main className="pb-20">
      <div className="max-w-frame mx-auto px-4 xl:px-0">
        <hr className="h-[1px] border-t-black/10 mb-5 sm:mb-6" />
        <BreadcrumbShop />

        {/* Invalid Category Notification */}
        {invalidCategory && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-yellow-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-800">
                  <strong>Category not found:</strong> The category "
                  {searchParams?.get("categories")}" doesn't exist. No products
                  found for this category.
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  Please check the category name and try again.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex md:space-x-5 items-start">
          {/* Sidebar Filters */}
          <div className="hidden md:block min-w-[295px] max-w-[295px] border border-black/10 rounded-[20px] px-5 md:px-6 py-5 space-y-5 md:space-y-6">
            <div className="flex items-center justify-between">
              <span className="font-bold text-black text-xl">Filters</span>
              <img
                src="/icons/filter.svg"
                alt="Filter"
                className="w-5 h-5 text-black/40"
              />
            </div>

            <Filters
              selectedCategory={selectedCategory}
              setSelectedCategory={setSelectedCategory}
              activeFilter={activeFilter}
              setActiveFilter={setActiveFilter}
              updateURL={updateURL}
              setCurrentPage={setCurrentPage}
            />
          </div>

          {/* Main Content */}
          <div className="flex flex-col w-full space-y-5">
            <div className="flex flex-col lg:flex-row lg:justify-between">
              <div className="flex items-center justify-between mb-2">
                {/* <h1 className="font-bold text-2xl md:text-[32px]">Shop</h1> */}
                <MobileFilters
                  selectedCategory={selectedCategory}
                  setSelectedCategory={setSelectedCategory}
                  activeFilter={activeFilter}
                  setActiveFilter={setActiveFilter}
                  updateURL={updateURL}
                  setCurrentPage={setCurrentPage}
                />
                <div className="flex items-center block md:hidden">
                  Sort by:{" "}
                  <Select
                    value={sort}
                    onValueChange={(value) => {
                      setSort(value);
                      updateURL({ sort: value, page: 1 });
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger className="focus:ring-0 font-medium text-sm px-1.5 sm:text-base w-fit text-black bg-transparent shadow-none border-none">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {/* <SelectItem value="most-popular">Most Popular</SelectItem> */}
                      <SelectItem value="low-price">Low Price</SelectItem>
                      <SelectItem value="high-price">High Price</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Sorting */}
              <div className="flex flex-col sm:items-center sm:flex-row">
                <span className="text-sm md:text-base text-black/60 mr-3">
                  Showing {firstProductIndex} to{" "}
                  {Math.min(lastProductIndex, totalRecord)} of {totalRecord}{" "}
                  Products
                </span>

                <div className="flex items-center hidden md:inline-flex">
                  Sort by:{" "}
                  <Select
                    value={sort}
                    onValueChange={(value) => {
                      setSort(value);
                      updateURL({ sort: value, page: 1 });
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger className="focus:ring-0 font-medium text-sm px-1.5 sm:text-base w-fit text-black bg-transparent shadow-none border-none">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {/* <SelectItem value="most-popular">Most Popular</SelectItem> */}
                      <SelectItem value="low-price">Low Price</SelectItem>
                      <SelectItem value="high-price">High Price</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            {/* Products Grid */}
            <div className="w-full grid grid-cols-2 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-5">
              {loading ? (
                <SpinnerbLoader className="w-10 border-2 border-gray-300 border-r-gray-600" />
              ) : error ? (
                <p className="text-lg font-semibold text-red-500 col-span-full">
                  {error}
                </p>
              ) : products && products?.length > 0 ? (
                products.map((product) => (
                  <ProductCard key={product.id} data={product} />
                ))
              ) : null}
            </div>

            {/* Display "No products found" message outside the grid */}
            {!loading && !error && products?.length === 0 && (
              <div className="text-center py-8">
                <p className="text-lg font-semibold text-gray-500 mb-2">
                  No products found.
                </p>
                {invalidCategory && (
                  <p className="text-sm text-gray-400">
                    The category "{searchParams?.get("categories")}" doesn't
                    exist.
                  </p>
                )}
              </div>
            )}
            {/* Pagination */}
            <hr className="border-t-black/10" />
            {!loading &&
              !error &&
              products?.length > 0 &&
              totalRecord > itemsPerPage && (
                <Pagination className="flex flex-col sm:flex-row justify-between items-center gap-4">
                  {/* Previous Button - Only show if not on first page */}
                  {currentPage > 1 ? (
                    <PaginationPrevious
                      href={generatePageURL(Math.max(currentPage - 1, 1))}
                      className="border border-primary text-primary px-3 py-1 rounded"
                      onClick={(e) => {
                        e.preventDefault();
                        const newPage = Math.max(currentPage - 1, 1);
                        setCurrentPage(newPage);
                        updateURL({ page: newPage });
                      }}
                    >
                      Previous
                    </PaginationPrevious>
                  ) : (
                    <span className="invisible border text-primary border-primary px-3 py-1 rounded">
                      Previous
                    </span>
                  )}

                  {/* Page Numbers */}
                  <PaginationContent className="flex space-x-1 sm:space-x-2 mx-auto overflow-x-auto max-w-full">
                    {(() => {
                      const totalPages = Math.ceil(totalRecord / itemsPerPage);
                      const items = [];

                      // Function to add a page number
                      const addPageNumber = (pageNum) => {
                        items.push(
                          <PaginationItem key={`page-${pageNum}`}>
                            <PaginationLink
                              href={generatePageURL(pageNum)}
                              className={`px-2 sm:px-3 py-1 rounded text-sm sm:text-base ${
                                currentPage === pageNum
                                  ? "bg-primary text-white"
                                  : "text-black/50 hover:text-black"
                              }`}
                              onClick={(e) => {
                                e.preventDefault();
                                setCurrentPage(pageNum);
                                updateURL({ page: pageNum });
                              }}
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      };

                      // Function to add ellipsis
                      const addEllipsis = (key) => {
                        items.push(
                          <PaginationItem key={`ellipsis-${key}`}>
                            <PaginationEllipsis className="text-black/50" />
                          </PaginationItem>
                        );
                      };

                      // Always add first page
                      addPageNumber(1);

                      // Logic for showing pages and ellipsis
                      if (currentPage > 3) {
                        addEllipsis("start");
                      }

                      // Pages around current page
                      for (
                        let i = Math.max(2, currentPage - 1);
                        i <= Math.min(totalPages - 1, currentPage + 1);
                        i++
                      ) {
                        addPageNumber(i);
                      }

                      // Add ellipsis if needed before last page
                      if (currentPage < totalPages - 2) {
                        addEllipsis("end");
                      }

                      // Always add last page if not the first page
                      if (totalPages > 1) {
                        addPageNumber(totalPages);
                      }

                      return items;
                    })()}
                  </PaginationContent>

                  {/* Next Button - Only show if not on last page */}
                  {currentPage < Math.ceil(totalRecord / itemsPerPage) ? (
                    <PaginationNext
                      href={generatePageURL(currentPage + 1)}
                      className="border border-primary text-primary px-3 py-1 rounded"
                      onClick={(e) => {
                        e.preventDefault();
                        const newPage = currentPage + 1;
                        setCurrentPage(newPage);
                        updateURL({ page: newPage });
                      }}
                    >
                      Next
                    </PaginationNext>
                  ) : (
                    <span className="invisible border text-primary border-primary px-3 py-1 rounded">
                      Next
                    </span>
                  )}
                </Pagination>
              )}
          </div>
        </div>
      </div>
    </main>
  );
}
