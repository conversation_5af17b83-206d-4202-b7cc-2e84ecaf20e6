import apiServiceWrapper from "@/lib/services/apiService";
import ProductClient from "./PageClient";
import { generateMetaTags, generateCanonicalUrl } from "@/lib/utils/seo";
import { notFound } from "next/navigation";

export async function generateMetadata({ params }) {
  try {
    const response = await apiServiceWrapper.get(`/products/${params.slug[0]}`);

    if (response.error || !response.data) {
      return {
        title: "Product Not Found | CromiTopia",
        description: "The product you're looking for could not be found.",
        robots: "noindex,nofollow",
      };
    }

    const product = response.data;

    // Generate comprehensive SEO metadata
    const keywords = [
      product.name,
      ...(product.categories || []).map((cat) => cat.name),
      ...(product.tags || []),
      "kawaii",
      "cute accessories",
      "japanese products",
      "online shopping",
    ];

    const metadata = generateMetaTags({
      title: product.name,
      description:
        product.description ||
        product.short_description ||
        `Shop ${product.name} at CromiTopia. High-quality kawaii products with worldwide shipping.`,
      keywords,
      image: product.images?.[0],
      url: `/shop/product/${product.slug}`,
      type: "website", // Use "website" instead of "product"
      canonical: generateCanonicalUrl(`/shop/product/${product.slug}`),
    });

    return metadata;
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Product | CromiTopia",
      description: "Shop kawaii products at CromiTopia",
    };
  }
}

export default async function ProductPage({ params }) {
  let productData;
  let relativeProducts = [];

  try {
    const response = await apiServiceWrapper.get(`/products/${params.slug[0]}`);

    if (response.error || !response.data) {
      console.error("Product not found:", response.message);
      notFound();
    }

    productData = {
      ...response.data,
      attribute_sets: response?.attribute_sets,
    };

    relativeProducts = response?.data?.related_products ?? [];
  } catch (error) {
    console.error("Error fetching product:", error);
    notFound();
  }

  return (
    <main className="min-h-screen bg-gray-50">
      <ProductClient
        productData={productData}
        relativeProducts={relativeProducts}
      />
    </main>
  );
}
