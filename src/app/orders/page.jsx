"use client";

import React, { useState, useEffect } from "react";
import { MapPin, Package, FileText, Star } from "lucide-react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

const MyOrders = () => {
  const [sortBy, setSortBy] = useState("newest");
  const [expandedId, setExpandedId] = useState(null);

  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem("token");

    const fetchOrders = async () => {
      try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/orders`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!res.ok) throw new Error("Failed to fetch orders");

        const data = await res.json();
        setOrders(data?.data || []);
      } catch (error) {
        toast.error("Error loading orders.");
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [router]);

  const toggleOrderDetails = (orderId) => {
    setExpandedId((prevId) => (prevId === orderId ? null : orderId));
  };

  const statusColors = {
    pending: "bg-yellow-100 text-yellow-700",
    processing: "bg-blue-100 text-blue-700",
    completed: "bg-green-100 text-green-700",
    canceled: "bg-red-100 text-red-700",
    partial_returned: "bg-orange-100 text-orange-700",
    returned: "bg-purple-100 text-purple-700",
  };

  const getInvoice = (orderId) => {
    const token = localStorage.getItem("token");

    const fetchInvoice = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/orders/${orderId}/invoice`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) throw new Error("Failed to fetch invoice");

        const data = await res.json();
        if (data.url) {
          window.open(data.url, "_blank");
        } else {
          throw new Error("Invoice URL not found");
        }
      } catch (error) {
        toast.error("Error fetching invoice.");
      }
    };

    fetchInvoice();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Orders</h1>
            <p className="text-gray-600 mt-1">Track and manage your orders</p>
          </div>
          {/* {orders.length > 0 && (
            <div className="mt-4 sm:mt-0 flex items-center gap-2">
              <span className="text-sm text-gray-600">Sort by</span>
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="pl-3 pr-8 py-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
          )} */}
        </div>

        {/* Orders List */}
        <div className="space-y-6">
          {orders.length > 0 ? (
            orders.map((order) => (
              <div
                key={order.code}
                className="bg-white cursor-pointer rounded-lg shadow-sm border border-gray-200"
              >
                <div className="p-6">
                  {/* Order Header */}
                  <div
                    className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6"
                    onClick={() => toggleOrderDetails(order.id)}
                  >
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Order {order.code}
                      </h3>
                      <p className="text-gray-500 mt-1">{order.order_date}</p>
                    </div>
                    <div className="flex items-center gap-3 mt-4 sm:mt-0">
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${
                          statusColors[order.status?.value] ||
                          "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {order.status?.value}
                      </span>
                      <div className="flex items-center gap-2">
                        {order.status?.value === "completed" && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push("/reviews");
                            }}
                            className="flex items-center gap-1 text-green-600 hover:text-green-700 text-sm font-medium"
                          >
                            <Star className="w-4 h-4" />
                            Review
                          </button>
                        )}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            getInvoice(order.id);
                          }}
                          className="flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          <FileText className="w-4 h-4" />
                          Invoice
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Details */}
                  {expandedId === order.id && (
                    <div className="space-y-4">
                      <div className="space-y-4 mb-6">
                        {order.products.map((item, idx) => (
                          <div key={idx} className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                              {item?.product_image ? (
                                <img
                                  src={item?.product_image}
                                  alt={item.product_name}
                                  className="object-cover rounded-md"
                                />
                              ) : (
                                <Package className="w-6 h-6 text-gray-400" />
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">
                                {item.product_name}
                              </h4>
                              <p className="text-gray-500 text-sm">
                                Qty: {item.qty}
                              </p>
                            </div>
                            <div className="text-right flex flex-col items-end gap-2">
                              <p className="font-medium text-gray-900">
                                {item.price_formatted}
                              </p>
                              {order.status?.value === "completed" && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    router.push("/reviews");
                                  }}
                                  className="flex items-center gap-1 text-green-600 hover:text-green-700 text-xs font-medium"
                                >
                                  <Star className="w-3 h-3" />
                                  Review Product
                                </button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Shipping Address */}
                      <div className="flex items-start gap-2 mb-6 p-3 bg-gray-50 rounded-lg">
                        <MapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                        <p className="text-gray-700 text-sm">
                          {order.shipping_address}
                        </p>
                      </div>

                      <div className="flex justify-between">
                        {/* <p className="text-lg font-semibold text-gray-900">
                          Order Tax: {order.tax_amount_formatted}
                        </p> */}
                        <p className="text-lg font-semibold text-gray-900">
                          Order Total: {order.amount_formatted}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center mt-10">
              <p className="mb-4 text-lg font-medium">
                It seems like you haven’t placed any orders yet!
              </p>
              <button
                className="bg-primary text-white py-2 px-4 rounded transition"
                onClick={() => router.push("/shop")}
              >
                Shop Now
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MyOrders;
