import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { compareArrays } from "@/lib/utils";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

const initialState = {
  items: [],
  isWishlistOpen: false,
  status: "idle", // idle, loading, succeeded, failed
  error: null,
};

// Async thunk to fetch wishlist items
export const fetchWishlist = createAsyncThunk(
  "wishlist/fetchWishlist",
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiServiceWrapper.get(
        `${process.env.NEXT_PUBLIC_API_URL}${apiRoutes.WISHLIST}`
      );
      return response.data?.items;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Async thunk to add item to wishlist
export const addToWishlistApi = createAsyncThunk(
  "wishlist/addToWishlist",
  async (item, { rejectWithValue }) => {
    try {
      const wishlist_id = localStorage.getItem("wishlist_id");
      let url = `${process.env.NEXT_PUBLIC_API_URL}${apiRoutes.WISHLIST}`;
      if (wishlist_id != "undefined") {
        url = `${process.env.NEXT_PUBLIC_API_URL}${apiRoutes.WISHLIST}/${wishlist_id}`;
      }
      const response = await apiServiceWrapper.post(url, item);
      localStorage.setItem("wishlist_id", response.id);
      return response.data?.items;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Async thunk to remove item from wishlist
export const removeFromWishlistApi = createAsyncThunk(
  "wishlist/removeFromWishlist",
  async (item, { rejectWithValue }) => {
    try {
      const wishlist_id = localStorage.getItem("wishlist_id");

      if (wishlist_id == "undefined") return;
      const response = await apiServiceWrapper.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/wishlist/${wishlist_id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
          data: { product_id: item.id }, // Pass attributes in body if needed
        }
      );
      return response.data?.items; // Return item for local state update
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const wishlistSlice = createSlice({
  name: "wishlist",
  initialState,
  reducers: {
    toggleWishlist: (state) => {
      state.isWishlistOpen = !state.isWishlistOpen;
    },
    closeWishlist: (state) => {
      state.isWishlistOpen = false;
    },
    clearWishlist: (state) => {
      state.items = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Wishlist
      .addCase(fetchWishlist.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchWishlist.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.items = action.payload;
      })
      .addCase(fetchWishlist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // Add to Wishlist
      .addCase(addToWishlistApi.fulfilled, (state, action) => {
        const isItemInWishlist =
          state?.items &&
          state.items.some((item) => item.id === action.payload.id);
        if (!isItemInWishlist) {
          state.items = [...action.payload];
        }
      })

      // Remove from Wishlist
      .addCase(removeFromWishlistApi.fulfilled, (state, action) => {
        state.items = action.payload;
      });
  },
});

export const { toggleWishlist, closeWishlist, clearWishlist } =
  wishlistSlice.actions;

export default wishlistSlice.reducer;
