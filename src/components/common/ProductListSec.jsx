"use client";

import React, { useEffect, useState } from "react";
import * as motion from "framer-motion/client";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import SpinnerbLoader from "../ui/SpinnerbLoader";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

const ProductListSec = ({ title, data, viewAllLink }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [clickingCategory, setClickingCategory] = useState(null); // Track which category is being clicked

  const router = useRouter();

  const handleCategoryClick = async (category) => {
    // Prevent multiple clicks
    if (clickingCategory) return;

    setClickingCategory(category.id);

    try {
      // Navigate to dedicated category page
      await router.push(`/category/${category.slug}`, { scroll: false });
    } catch (error) {
      console.error("Navigation error:", error);
    } finally {
      // Reset loading state after a short delay to ensure navigation completes
      setTimeout(() => {
        setClickingCategory(null);
      }, 1000);
    }
  };

  const handleViewAllClick = async () => {
    // Prevent multiple clicks
    if (clickingCategory) return;

    setClickingCategory("view-all");

    try {
      // Navigate to shop page
      await router.push("/shop", { scroll: false });
    } catch (error) {
      console.error("Navigation error:", error);
    } finally {
      // Reset loading state after a short delay to ensure navigation completes
      setTimeout(() => {
        setClickingCategory(null);
      }, 1000);
    }
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const fetchCategories = await apiServiceWrapper.get(apiRoutes.CATEGORY);
        const result = await fetchCategories;

        if (result.error === false && result.data) {
          // const decryptedText = decryptData(result.data);
          // const categoryList = decryptedText && JSON.parse(decryptedText);
          const categoryList = result.data;
          setCategories(categoryList ?? []);
        } else {
          throw new Error(result.message || "Failed to fetch categories");
        }
      } catch (error) {
        console.error("Category fetch error:", error);
        setError(
          error.message || "An error occurred while fetching categories"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <section className="max-w-frame mx-auto text-center">
      <div className="w-full px-4">
        {/* Animated Title */}
        <motion.h2
          initial={{ y: "100px", opacity: 0 }}
          whileInView={{ y: "0", opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className={cn(["text-[32px] md:text-5xl mb-8 md:mb-14 font-bold"])}
        >
          {title}
        </motion.h2>

        {/* Loading & Error Handling */}
        {loading ? (
          <SpinnerbLoader className="w-10 border-2 border-gray-300 border-r-gray-600" />
        ) : error ? (
          <p className="text-red-500">{error}</p>
        ) : (
          <>
            {/* Grid for Mobile */}
            <div className="grid grid-cols-2 gap-4 md:hidden">
              {categories.length > 0 ? (
                <>
                  {categories.map((category) => {
                    const isClicking = clickingCategory === category.id;
                    return (
                      <div
                        key={category.id}
                        className={`flex flex-col items-center cursor-pointer ${
                          isClicking ? "opacity-60 pointer-events-none" : ""
                        }`}
                        onClick={() => handleCategoryClick(category)}
                      >
                        <div
                          className={cn(
                            "relative p-1 w-24 h-24 bg-white rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 overflow-hidden"
                          )}
                        >
                          {isClicking && (
                            <div className="absolute inset-0 bg-white/80 rounded-full flex items-center justify-center z-10">
                              <SpinnerbLoader className="w-6 h-6 border-2 border-gray-300 border-r-gray-600" />
                            </div>
                          )}
                          <motion.div
                            whileHover={{ scale: isClicking ? 1 : 1.04 }}
                            transition={{ duration: 0.3 }}
                            className="w-full h-full"
                          >
                            {category?.image_with_sizes?.origin ? (
                              <img
                                src={category?.image_with_sizes?.origin}
                                alt={category.name}
                                className="w-full h-full object-cover rounded-full select-none"
                                style={{ aspectRatio: "1/1" }}
                              />
                            ) : (
                              <p className="text-gray-400">No Image</p>
                            )}
                          </motion.div>
                        </div>
                        <p className="mt-2 text-sm font-semibold text-center">
                          {category.name}
                        </p>
                      </div>
                    );
                  })}

                  {/* View All Option */}
                  <div
                    className={`flex flex-col items-center cursor-pointer ${
                      clickingCategory === "view-all"
                        ? "opacity-60 pointer-events-none"
                        : ""
                    }`}
                    onClick={handleViewAllClick}
                  >
                    <div
                      className={cn(
                        "relative p-1 w-24 h-24 bg-white rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 overflow-hidden"
                      )}
                    >
                      {clickingCategory === "view-all" && (
                        <div className="absolute inset-0 bg-white/80 rounded-full flex items-center justify-center z-10">
                          <SpinnerbLoader className="w-6 h-6 border-2 border-gray-300 border-r-gray-600" />
                        </div>
                      )}
                      <motion.div
                        whileHover={{
                          scale: clickingCategory === "view-all" ? 1 : 1.04,
                        }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <img
                          src={"/images/all.webp"}
                          alt={"View All Products"}
                          className="w-full h-full object-cover rounded-full select-none"
                          style={{ aspectRatio: "1/1" }}
                        />
                      </motion.div>
                    </div>
                    <p className="mt-2 text-sm font-semibold text-center">
                      View All
                    </p>
                  </div>
                </>
              ) : (
                <p className="text-gray-500 col-span-2">
                  No categories available
                </p>
              )}
            </div>

            {/* Grid for Larger Screens */}
            <div className="hidden md:grid grid-cols-5 gap-6">
              {categories.length > 0 ? (
                <>
                  {categories.map((category) => {
                    const isClicking = clickingCategory === category.id;
                    return (
                      <div
                        key={category.id}
                        className={`flex flex-col items-center cursor-pointer ${
                          isClicking ? "opacity-60 pointer-events-none" : ""
                        }`}
                        onClick={() => handleCategoryClick(category)}
                      >
                        <div
                          className={cn(
                            "relative p-2 w-44 h-44 bg-white rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 overflow-hidden"
                          )}
                        >
                          {isClicking && (
                            <div className="absolute inset-0 bg-white/80 rounded-full flex items-center justify-center z-10">
                              <SpinnerbLoader className="w-8 h-8 border-2 border-gray-300 border-r-gray-600" />
                            </div>
                          )}
                          <motion.div
                            whileHover={{ scale: isClicking ? 1 : 1.04 }}
                            transition={{ duration: 0.3 }}
                            className="w-full h-full rounded-full overflow-hidden"
                          >
                            {category?.image_with_sizes?.origin ? (
                              <img
                                src={category?.image_with_sizes?.origin}
                                alt={category.name}
                                className="w-full h-full object-cover select-none"
                                style={{ aspectRatio: "1/1" }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <p className="text-gray-400">No Image</p>
                              </div>
                            )}
                          </motion.div>
                        </div>
                        <p className="mt-2 text-lg font-semibold text-center">
                          {category.name}
                        </p>
                      </div>
                    );
                  })}

                  {/* View All Option */}
                  <div
                    className={`flex flex-col items-center cursor-pointer ${
                      clickingCategory === "view-all"
                        ? "opacity-60 pointer-events-none"
                        : ""
                    }`}
                    onClick={handleViewAllClick}
                  >
                    <div
                      className={cn(
                        "relative p-2 w-44 h-44 bg-white rounded-full flex items-center justify-center border-2 border-dashed border-gray-300 overflow-hidden"
                      )}
                    >
                      {clickingCategory === "view-all" && (
                        <div className="absolute inset-0 bg-white/80 rounded-full flex items-center justify-center z-10">
                          <SpinnerbLoader className="w-8 h-8 border-2 border-gray-300 border-r-gray-600" />
                        </div>
                      )}
                      <motion.div
                        whileHover={{
                          scale: clickingCategory === "view-all" ? 1 : 1.04,
                        }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full rounded-full overflow-hidden"
                      >
                        <img
                          src={"/images/all.webp"}
                          alt={"View All Products"}
                          className="w-full h-full object-cover select-none"
                          style={{ aspectRatio: "1/1" }}
                        />
                      </motion.div>
                    </div>
                    <p className="mt-2 text-lg font-semibold text-center">
                      View All Products
                    </p>
                  </div>
                </>
              ) : (
                <p className="text-gray-500 col-span-5">
                  No categories available
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default ProductListSec;
