import React, { useState } from "react";
import {
  Drawer,
  Drawer<PERSON>lose,
  Drawer<PERSON>ontent,
  DrawerD<PERSON><PERSON>,
  Drawer<PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import Filters from ".";

const MobileFilters = ({
  selectedCategory,
  setSelectedCategory,
  activeFilter,
  setActiveFilter,
  updateURL,
  setCurrentPage,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Function to handle filter selection and auto-close
  const handleFilterChange = (callback) => {
    callback();
    // Auto-close drawer after filter selection
    setTimeout(() => {
      setIsOpen(false);
    }, 300);
  };

  return (
    <>
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          <button
            type="button"
            className="h-8 w-8 rounded-full bg-[#F0F0F0] text-black p-1 md:hidden"
            onClick={() => setIsO<PERSON>(true)}
          >
            <img
              src="/icons/filter.svg"
              alt="Filter"
              className="w-5 h-5 text-black/40"
            />
          </button>
        </DrawerTrigger>
        <DrawerContent className="max-h-[90%]">
          <DrawerHeader>
            <div className="flex items-center justify-between">
              <span className="font-bold text-black text-xl">Filters</span>
              <img
                src="/icons/filter.svg"
                alt="Filter"
                className="w-5 h-5 text-black/40"
              />
            </div>
            <DrawerTitle className="hidden">filters</DrawerTitle>
            <DrawerDescription className="hidden">filters</DrawerDescription>
          </DrawerHeader>
          <div className="max-h-[90%] overflow-y-auto w-full px-5 md:px-6 py-5 space-y-5 md:space-y-6">
            <Filters
              activeFilter={activeFilter}
              setActiveFilter={setActiveFilter}
              selectedCategory={selectedCategory}
              setSelectedCategory={setSelectedCategory}
              updateURL={updateURL}
              setCurrentPage={setCurrentPage}
              onFilterChange={handleFilterChange}
              isMobile={true}
            />
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default MobileFilters;
