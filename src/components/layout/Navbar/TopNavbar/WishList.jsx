"use client";

import { useAppSelector } from "@/lib/hooks/redux";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const WishList = () => {
  const { items } = useAppSelector((state) => state.wishlist);

  return (
    <Link href="/wishlist" className="relative p-1">
      <Image
        priority
        src="/icons/favorite.svg"
        height={100}
        width={100}
        alt="wishlist"
        className="max-w-[22px] max-h-[22px]"
      />
      {items?.length > 0 && (
        <span className="border bg-black text-center text-white rounded-full w-fit h-fit px-1 text-xs absolute -top-[0.5rem] left-[79%] -translate-x-1/2">
          {items?.length}
        </span>
      )}
    </Link>
  );
};

export default WishList;
